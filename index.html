<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Calculator</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background-color: #f2f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .calculator {
      background-color: #222;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      width: 350px;
      display: flex;
      flex-direction: column;
    }
    .calculator input {
      width: 100%;
      height: 70px;
      text-align: right;
      font-size: 2.5em;
      margin-bottom: 20px;
      padding: 10px;
      background-color: #1e1e1e;
      color: white;
      border: none;
      border-radius: 10px;
      box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
    }
    .calculator button {
      width: 70px;
      height: 70px;
      font-size: 1.5em;
      margin: 10px;
      border: none;
      border-radius: 10px;
      background-color: #333;
      color: white;
      transition: background-color 0.2s ease, transform 0.1s ease;
    }
    .calculator button:hover {
      background-color: #444;
    }
    .calculator button:active {
      transform: scale(0.95);
    }
    .calculator button.clear {
      background-color: #e74c3c;
    }
    .calculator button.equal {
      background-color: #2ecc71;
    }
    .calculator button.clear:hover {
      background-color: #c0392b;
    }
    .calculator button.equal:hover {
      background-color: #27ae60;
    }
    .calculator button.operator {
      background-color: #f39c12;
    }
    .calculator button.operator:hover {
      background-color: #e67e22;
    }
  </style>
</head>
<body>
  <div class="calculator">
    <input type="text" id="display" disabled>
    <div>
      <button class="clear" onclick="clearDisplay()">C</button>
      <button onclick="appendToDisplay('7')">7</button>
      <button onclick="appendToDisplay('8')">8</button>
      <button onclick="appendToDisplay('9')">9</button>
      <button class="operator" onclick="appendToDisplay('/')">/</button>
    </div>
    <div>
      <button onclick="appendToDisplay('4')">4</button>
      <button onclick="appendToDisplay('5')">5</button>
      <button onclick="appendToDisplay('6')">6</button>
      <button class="operator" onclick="appendToDisplay('*')">*</button>
    </div>
    <div>
      <button onclick="appendToDisplay('1')">1</button>
      <button onclick="appendToDisplay('2')">2</button>
      <button onclick="appendToDisplay('3')">3</button>
      <button class="operator" onclick="appendToDisplay('-')">-</button>
    </div>
    <div>
      <button onclick="appendToDisplay('0')">0</button>
      <button onclick="appendToDisplay('.')">.</button>
      <button class="equal" onclick="calculate()">=</button>
      <button class="operator" onclick="appendToDisplay('+')">+</button>
    </div>
  </div>

  <script>
    function clearDisplay() {
      document.getElementById('display').value = '';
    }

    function appendToDisplay(value) {
      document.getElementById('display').value += value;
    }

    function calculate() {
      let display = document.getElementById('display');
      try {
        display.value = eval(display.value); // Evaluate the expression
      } catch (e) {
        display.value = 'Error';
      }
    }
  </script>
</body>
</html>
