<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Calculator</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background-color: #f2f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .calculator {
      background-color: #222;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      width: 320px;
      display: flex;
      flex-direction: column;
    }
    .calculator input {
      width: calc(100% - 20px);
      height: 70px;
      text-align: right;
      font-size: 2.5em;
      margin-bottom: 20px;
      padding: 10px;
      background-color: #1e1e1e;
      color: white;
      border: none;
      border-radius: 10px;
      box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
      box-sizing: border-box;
    }
    .button-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .calculator button {
      width: 65px;
      height: 65px;
      font-size: 1.5em;
      border: none;
      border-radius: 10px;
      background-color: #333;
      color: white;
      transition: background-color 0.2s ease, transform 0.1s ease;
      cursor: pointer;
    }
    .calculator button:hover {
      background-color: #444;
    }
    .calculator button:active {
      transform: scale(0.95);
    }
    .calculator button.clear {
      background-color: #e74c3c;
    }
    .calculator button.equal {
      background-color: #2ecc71;
    }
    .calculator button.clear:hover {
      background-color: #c0392b;
    }
    .calculator button.equal:hover {
      background-color: #27ae60;
    }
    .calculator button.operator {
      background-color: #f39c12;
    }
    .calculator button.operator:hover {
      background-color: #e67e22;
    }
    .button-row:last-child {
      margin-bottom: 0;
    }
    .calculator button:focus {
      outline: none;
      box-shadow: 0 0 0 2px #3498db;
    }
  </style>
</head>
<body>
  <div class="calculator">
    <input type="text" id="display" disabled>
    <div class="button-row">
      <button class="clear" onclick="clearDisplay()">C</button>
      <button onclick="deleteLast()">⌫</button>
      <button onclick="appendToDisplay('/')" class="operator">÷</button>
      <button onclick="appendToDisplay('*')" class="operator">×</button>
    </div>
    <div class="button-row">
      <button onclick="appendToDisplay('7')">7</button>
      <button onclick="appendToDisplay('8')">8</button>
      <button onclick="appendToDisplay('9')">9</button>
      <button class="operator" onclick="appendToDisplay('-')">-</button>
    </div>
    <div class="button-row">
      <button onclick="appendToDisplay('4')">4</button>
      <button onclick="appendToDisplay('5')">5</button>
      <button onclick="appendToDisplay('6')">6</button>
      <button class="operator" onclick="appendToDisplay('+')">+</button>
    </div>
    <div class="button-row">
      <button onclick="appendToDisplay('1')">1</button>
      <button onclick="appendToDisplay('2')">2</button>
      <button onclick="appendToDisplay('3')">3</button>
      <button class="equal" onclick="calculate()">=</button>
    </div>
    <div class="button-row">
      <button onclick="appendToDisplay('0')" style="flex: 2; margin-right: 10px;">0</button>
      <button onclick="appendToDisplay('.')">.</button>
    </div>
  </div>

  <script>
    function clearDisplay() {
      document.getElementById('display').value = '';
    }

    function deleteLast() {
      let display = document.getElementById('display');
      display.value = display.value.slice(0, -1);
    }

    function appendToDisplay(value) {
      document.getElementById('display').value += value;
    }

    function calculate() {
      let display = document.getElementById('display');
      try {
        // Replace display symbols with actual operators for calculation
        let expression = display.value.replace(/×/g, '*').replace(/÷/g, '/');
        display.value = eval(expression);
      } catch (e) {
        display.value = 'Error';
      }
    }
  </script>
</body>
</html>
