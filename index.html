<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Calculator</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      background-color: #f2f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .calculator {
      background-color: #222;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      width: 320px;
      display: flex;
      flex-direction: column;
      transition: width 0.3s ease;
    }
    .calculator.scientific {
      width: 520px;
    }
    .calculator input {
      width: calc(100% - 20px);
      height: 70px;
      text-align: right;
      font-size: 2.5em;
      margin-bottom: 20px;
      padding: 10px;
      background-color: #1e1e1e;
      color: white;
      border: none;
      border-radius: 10px;
      box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
      box-sizing: border-box;
    }
    .button-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      gap: 8px;
    }
    .calculator button {
      flex: 1;
      height: 65px;
      font-size: 1.4em;
      font-weight: 500;
      border: none;
      border-radius: 12px;
      background-color: #333;
      color: white;
      transition: all 0.2s ease;
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .calculator button:hover {
      background-color: #444;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    .calculator button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    .calculator button.clear {
      background-color: #e74c3c;
    }
    .calculator button.equal {
      background-color: #2ecc71;
    }
    .calculator button.clear:hover {
      background-color: #c0392b;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
    }
    .calculator button.equal:hover {
      background-color: #27ae60;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(46, 204, 113, 0.4);
    }
    .calculator button.operator {
      background-color: #f39c12;
    }
    .calculator button.operator:hover {
      background-color: #e67e22;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
    }
    .button-row:last-child {
      margin-bottom: 0;
    }
    .calculator button:focus {
      outline: none;
      box-shadow: 0 0 0 2px #3498db;
    }
    .toggle-btn {
      background-color: #9b59b6 !important;
      margin-bottom: 15px;
      width: 120px;
      height: 40px;
      font-size: 1em;
      font-weight: 600;
      align-self: flex-end;
      border-radius: 20px;
      box-shadow: 0 2px 6px rgba(155, 89, 182, 0.3);
      transition: all 0.3s ease;
    }
    .toggle-btn:hover {
      background-color: #8e44ad !important;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(155, 89, 182, 0.4);
    }
    .toggle-btn:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(155, 89, 182, 0.3);
    }
    .scientific-panel {
      display: none;
      margin-right: 15px;
      min-width: 200px;
    }
    .calculator.scientific .scientific-panel {
      display: block;
    }
    .calculator-content {
      display: flex;
      gap: 15px;
    }
    .basic-panel {
      flex: 1;
    }
    .scientific-panel .button-row {
      margin-bottom: 12px;
      gap: 8px;
    }
    .scientific-panel button {
      flex: 1;
      height: 65px;
      font-size: 1.1em;
      font-weight: 500;
      background-color: #34495e;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
    }
    .scientific-panel button:hover {
      background-color: #2c3e50;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
  </style>
</head>
<body>
  <div class="calculator" id="calculator">
    <button class="toggle-btn" onclick="toggleScientific()">Scientific</button>
    <input type="text" id="display" readonly>

    <div class="calculator-content">
      <!-- Scientific Panel -->
      <div class="scientific-panel">
        <div class="button-row">
          <button onclick="appendFunction('sin(')">sin</button>
          <button onclick="appendFunction('cos(')">cos</button>
          <button onclick="appendFunction('tan(')">tan</button>
        </div>
        <div class="button-row">
          <button onclick="appendFunction('log(')">log</button>
          <button onclick="appendFunction('ln(')">ln</button>
          <button onclick="appendToDisplay('π')">π</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('^')">x^y</button>
          <button onclick="appendFunction('sqrt(')">√</button>
          <button onclick="appendToDisplay('e')">e</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('(')">(</button>
          <button onclick="appendToDisplay(')')">)</button>
          <button onclick="appendToDisplay('!')">x!</button>
        </div>
      </div>

      <!-- Basic Panel -->
      <div class="basic-panel">
        <div class="button-row">
          <button class="clear" onclick="clearDisplay()">C</button>
          <button onclick="deleteLast()">⌫</button>
          <button onclick="appendToDisplay('/')" class="operator">÷</button>
          <button onclick="appendToDisplay('*')" class="operator">×</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('7')">7</button>
          <button onclick="appendToDisplay('8')">8</button>
          <button onclick="appendToDisplay('9')">9</button>
          <button class="operator" onclick="appendToDisplay('-')">-</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('4')">4</button>
          <button onclick="appendToDisplay('5')">5</button>
          <button onclick="appendToDisplay('6')">6</button>
          <button class="operator" onclick="appendToDisplay('+')">+</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('1')">1</button>
          <button onclick="appendToDisplay('2')">2</button>
          <button onclick="appendToDisplay('3')">3</button>
          <button class="equal" onclick="calculate()">=</button>
        </div>
        <div class="button-row">
          <button onclick="appendToDisplay('0')" style="flex: 2;">0</button>
          <button onclick="appendToDisplay('.')">.</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    function clearDisplay() {
      document.getElementById('display').value = '';
    }

    function deleteLast() {
      let display = document.getElementById('display');
      display.value = display.value.slice(0, -1);
    }

    function appendToDisplay(value) {
      document.getElementById('display').value += value;
    }

    function appendFunction(func) {
      document.getElementById('display').value += func;
    }

    function toggleScientific() {
      const calculator = document.getElementById('calculator');
      const toggleBtn = document.querySelector('.toggle-btn');

      if (calculator.classList.contains('scientific')) {
        calculator.classList.remove('scientific');
        toggleBtn.textContent = 'Scientific';
      } else {
        calculator.classList.add('scientific');
        toggleBtn.textContent = 'Basic';
      }
    }

    function factorial(n) {
      if (n === 0 || n === 1) return 1;
      return n * factorial(n - 1);
    }

    function calculate() {
      let display = document.getElementById('display');
      try {
        let expression = display.value
          .replace(/×/g, '*')
          .replace(/÷/g, '/')
          .replace(/π/g, Math.PI)
          .replace(/e/g, Math.E)
          .replace(/sin\(/g, 'Math.sin(')
          .replace(/cos\(/g, 'Math.cos(')
          .replace(/tan\(/g, 'Math.tan(')
          .replace(/log\(/g, 'Math.log10(')
          .replace(/ln\(/g, 'Math.log(')
          .replace(/sqrt\(/g, 'Math.sqrt(')
          .replace(/\^/g, '**');

        // Handle factorial
        expression = expression.replace(/(\d+)!/g, function(match, num) {
          return factorial(parseInt(num));
        });

        display.value = eval(expression);
      } catch (e) {
        display.value = 'Error';
      }
    }

    // Add keyboard support
    document.addEventListener('keydown', function(event) {
      const key = event.key;

      // Numbers and decimal point
      if (key >= '0' && key <= '9') {
        appendToDisplay(key);
      } else if (key === '.') {
        appendToDisplay('.');
      }
      // Operators
      else if (key === '+') {
        appendToDisplay('+');
      } else if (key === '-') {
        appendToDisplay('-');
      } else if (key === '*') {
        appendToDisplay('×');
      } else if (key === '/') {
        event.preventDefault(); // Prevent browser search
        appendToDisplay('÷');
      } else if (key === '^') {
        appendToDisplay('^');
      } else if (key === '(' || key === ')') {
        appendToDisplay(key);
      }
      // Special keys
      else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        calculate();
      } else if (key === 'Escape' || key === 'c' || key === 'C') {
        clearDisplay();
      } else if (key === 'Backspace') {
        event.preventDefault();
        deleteLast();
      } else if (key === 's' && event.ctrlKey) {
        event.preventDefault();
        toggleScientific();
      }
    });

    // Focus on the display when page loads
    window.onload = function() {
      document.getElementById('display').focus();
    };
  </script>
</body>
</html>
